version: '3.8'

services:
  api:
    build:
      context: .
      target: production
    container_name: namecheap-proxy-api
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
      - NAMECHEAP_API_USER=${NAMECHEAP_API_USER:-blackingdev}
      - NAMECHEAP_API_KEY=${NAMECHEAP_API_KEY:-84523b0158f340c9be472d6a8d50c309}
      - NAMECHEAP_USERNAME=${NAMECHEAP_USERNAME:-blackingdev}
      - NAMECHEAP_CLIENT_IP=${NAMECHEAP_CLIENT_IP:-*************}
      - NAMECHEAP_BASE_URL=${NAMECHEAP_BASE_URL:-https://api.sandbox.namecheap.com/xml.response}
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: namecheap-proxy-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      api:
        condition: service_healthy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  nginx_logs:
    driver: local
