server {
    listen 80;
    server_name localhost;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Rate limiting
    limit_req zone=api burst=20 nodelay;

    # Proxy settings
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;

    # Health check endpoint
    location /health {
        proxy_pass http://api_backend/health;
    }

    # API routes
    location /api/ {
        proxy_pass http://api_backend/api/;
    }

    # Root redirect to health check
    location = / {
        return 302 /health;
    }

    # Handle favicon requests
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        return 204;
    }

    # Error pages
    error_page 404 /404.html;
    location = /404.html {
        return 404 '{"success": false, "error": "Not Found", "statusCode": 404}';
        add_header Content-Type application/json;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        return 500 '{"success": false, "error": "Internal Server Error", "statusCode": 500}';
        add_header Content-Type application/json;
    }
}
